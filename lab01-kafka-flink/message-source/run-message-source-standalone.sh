#!/bin/bash

# 消息源软件运行脚本（单机版）

set -e

echo "=== 启动消息源软件（单机版） ==="

# 检查环境变量
if [ -z "$KAFKA_HOME" ]; then
    echo "错误: KAFKA_HOME环境变量未设置"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 编译项目
echo "编译消息源软件项目..."
mvn clean package -DskipTests

if [ $? -ne 0 ]; then
    echo "项目编译失败"
    exit 1
fi

# 检查Kafka是否运行
echo "检查Kafka服务状态..."
if ! pgrep -f "kafka.Kafka" > /dev/null; then
    echo "错误: Kafka服务未运行，请先启动Kafka集群"
    exit 1
fi

# 检查JAR文件
JAR_FILE="target/message-source-1.0-SNAPSHOT.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在: $JAR_FILE"
    exit 1
fi

# 单机版配置
KAFKA_BROKERS="hadoop01:9092"
CONSUMER_GROUP="message-source-group"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--brokers)
            KAFKA_BROKERS="$2"
            shift 2
            ;;
        -g|--group)
            CONSUMER_GROUP="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -b, --brokers BROKERS    Kafka broker地址列表 (默认: $KAFKA_BROKERS)"
            echo "  -g, --group GROUP        消费者组ID (默认: $CONSUMER_GROUP)"
            echo "  -h, --help               显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo "配置信息:"
echo "  Kafka Brokers: $KAFKA_BROKERS"
echo "  消费者组: $CONSUMER_GROUP"

# 启动消息源软件
echo "启动消息源软件..."
java -jar $JAR_FILE \
    --brokers "$KAFKA_BROKERS" \
    --group "$CONSUMER_GROUP"

echo "消息源软件已退出"
