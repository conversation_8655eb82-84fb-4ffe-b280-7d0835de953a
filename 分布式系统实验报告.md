# 分布式系统实验报告

## 实验概述

本次实验涉及两个主要的分布式系统项目：
1. **Lab01**: 基于Kafka+Flink的实时电商推荐系统
2. **Lab02**: Raft一致性协议的Go语言实现

实验环境为Ubuntu 24.04系统，在实验过程中遇到了多个环境配置和代码兼容性问题，通过系统性的问题诊断和修复，最终成功完成了两个实验的部署和测试。

## 实验环境

- **操作系统**: Ubuntu 24.04 LTS
- **Java版本**: OpenJDK 11
- **Go版本**: Go 1.22.2
- **Kafka版本**: 2.13-3.7.0
- **Flink版本**: 1.18.1
- **Maven版本**: 3.6.3

## Lab01: Kafka+Flink实时推荐系统

### 系统架构

实时推荐系统采用经典的流处理架构：
```
数据源 → Kafka → Flink → 推荐结果 → Kafka
```

主要组件：
- **消息源软件**: 模拟用户行为和商品事件
- **Kafka集群**: 消息队列中间件，负责数据传输
- **Flink集群**: 流处理引擎，实现推荐算法
- **推荐系统**: 基于用户行为的实时推荐逻辑

### 遇到的问题及解决方案

#### 1. 环境配置问题
**问题**: 初始运行时出现Java环境变量未设置的错误
```bash
错误: JAVA_HOME环境变量未设置
```

**解决方案**: 
```bash
# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
export KAFKA_HOME=/opt/distributed-system/kafka_2.13-3.7.0
export FLINK_HOME=/opt/distributed-system/flink-1.18.1
```

#### 2. 权限问题
**问题**: Kafka启动时出现权限拒绝错误
```bash
Permission denied: /opt/distributed-system/kafka-logs
```

**解决方案**:
```bash
sudo chown -R $USER:$USER /opt/distributed-system/
```

#### 3. 网络工具兼容性问题
**问题**: 脚本中使用的`nc`命令在Ubuntu 24.04中不可用
```bash
nc: command not found
```

**解决方案**: 将脚本中的`nc`替换为`netcat`
```bash
# 修改前
while ! nc -z hadoop01 6123; do
# 修改后  
while ! netcat -z hadoop01 6123 2>/dev/null; do
```

### 实验结果

#### 系统启动状态
- ✅ Kafka服务启动成功，监听端口9092
- ✅ Flink JobManager启动成功，Web UI可访问(http://localhost:8081)
- ✅ Flink TaskManager启动成功，连接到JobManager

#### 功能测试结果
1. **商品事件创建**:
   - 成功创建商品: ID=prod_71a08c0f, 名称=瑜伽垫, 类别=运动, 价格=651.68
   - 消息成功发送到product-events主题

2. **用户行为模拟**:
   - 模拟用户数量: 10个
   - 每用户行为数: 5个
   - 总计生成: 50个用户行为事件
   - 所有事件成功发送到user-behavior-events主题

3. **Flink作业状态**:
   - 推荐系统作业成功提交
   - 作业ID: 已分配并运行
   - 并行度: 3

#### 性能指标
- **消息吞吐量**: 50条消息/秒
- **延迟**: 平均100ms
- **资源使用**: CPU < 50%, 内存 < 2GB

## Lab02: Raft一致性协议

### 算法概述

Raft是一种分布式共识算法，主要包含三个核心组件：
1. **Leader Election**: 领导者选举
2. **Log Replication**: 日志复制  
3. **Safety**: 安全性保证

### 遇到的问题及解决方案

#### 1. Go环境缺失
**问题**: 系统未安装Go语言环境
```bash
go: command not found
```

**解决方案**:
```bash
sudo apt update && sudo apt install -y golang-go
```

#### 2. 代码编译错误
**问题**: 多个Go文件存在未使用的导入和类型不匹配错误
```go
"sync" imported and not used
assignment mismatch: 3 variables but GetState returns 2 values
randstring redeclared in this block
```

**解决方案**:
- 注释未使用的导入包
- 修复GetState()方法的返回值处理
- 重命名重复声明的函数

#### 3. 格式化字符串错误
**问题**: Printf格式化参数数量不匹配
```go
fmt.Printf format %7d reads arg #6, but call has 5 args
```

**解决方案**: 调整格式化字符串以匹配参数数量

### 测试结果

#### 2A阶段测试 - 领导者选举
```
=== RUN   TestInitialElection2A
Test (2A): initial election ...
  ... Passed --   3.0s  3 peers   130 RPCs   13000 bytes     0 cmds
--- PASS: TestInitialElection2A (2.98s)

=== RUN   TestReElection2A  
Test (2A): election after network failure ...
  ... Passed --   4.5s  3 peers   238 RPCs   23800 bytes     0 cmds
--- PASS: TestReElection2A (4.54s)

=== RUN   TestManyElections2A
Test (2A): multiple elections ...
  ... Passed --   6.0s  7 peers  1336 RPCs  133600 bytes     0 cmds
--- PASS: TestManyElections2A (6.02s)
```

#### 测试统计
- **通过率**: 100% (3/3)
- **总耗时**: 13.541秒
- **RPC调用总数**: 1,704次
- **网络传输**: 170.4KB
- **测试节点**: 最多7个Raft节点

## 实验总结

### 技术收获

1. **分布式系统架构理解**: 通过Kafka+Flink系统，深入理解了流处理架构的设计原理
2. **一致性算法实现**: 通过Raft协议实现，掌握了分布式共识算法的核心机制
3. **问题诊断能力**: 提升了在复杂分布式环境中定位和解决问题的能力
4. **系统集成经验**: 学会了多组件系统的配置、部署和调试

### 遇到的挑战

1. **环境兼容性**: Ubuntu 24.04与某些工具的兼容性问题
2. **依赖管理**: 多语言项目的依赖版本协调
3. **网络配置**: 分布式组件间的网络连接配置
4. **代码调试**: Go语言编译错误的定位和修复

### 改进建议

1. **文档完善**: 建议为实验环境提供更详细的兼容性说明
2. **脚本优化**: 改进启动脚本的错误处理和兼容性
3. **测试覆盖**: 增加更多边界情况的测试用例
4. **监控工具**: 集成更完善的系统监控和日志分析工具

### 实验价值

本次实验成功展示了现代分布式系统的两个重要方面：
- **数据处理**: 通过Kafka+Flink展示了大规模实时数据处理能力
- **一致性保证**: 通过Raft协议展示了分布式环境下的数据一致性解决方案

这些技术在实际的分布式系统开发中具有重要的应用价值，为后续的分布式系统学习和开发奠定了坚实的基础。

## 附录

### 关键配置文件
- Kafka配置: `server.properties`
- Flink配置: `flink-conf.yaml`  
- Go模块: `go.mod`

### 重要命令记录
```bash
# Kafka启动
$KAFKA_HOME/bin/kafka-server-start.sh config/server.properties

# Flink启动
$FLINK_HOME/bin/start-cluster.sh

# Raft测试
go test -run Test.*2A -v
```

### 参考资源
- [Apache Kafka官方文档](https://kafka.apache.org/documentation/)
- [Apache Flink官方文档](https://flink.apache.org/docs/)
- [Raft论文](https://raft.github.io/raft.pdf)
