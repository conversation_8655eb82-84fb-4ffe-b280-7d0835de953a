package raft

import (
	"bytes"
	"encoding/gob"
	// "fmt" // 暂时注释掉未使用的导入
	"log"
	"math/rand"
	"time"
)

// Debugging
const Debug = false

func DPrintf(format string, a ...interface{}) (n int, err error) {
	if Debug {
		log.Printf(format, a...)
	}
	return
}

// RandomElectionTimeout returns a random election timeout between 150-300ms
func RandomElectionTimeout() time.Duration {
	return time.Duration(150+rand.Intn(150)) * time.Millisecond
}

// StableHeartbeatTimeout returns a stable heartbeat timeout of 50ms
func StableHeartbeatTimeout() time.Duration {
	return 50 * time.Millisecond
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// insertionSort sorts an array of integers using insertion sort
func insertionSort(arr []int) {
	for i := 1; i < len(arr); i++ {
		key := arr[i]
		j := i - 1
		for j >= 0 && arr[j] > key {
			arr[j+1] = arr[j]
			j--
		}
		arr[j+1] = key
	}
}

// shrinkEntriesArray shrinks the entries array to reduce memory usage
func shrinkEntriesArray(entries []LogEntry) []LogEntry {
	const lenMultiple = 2
	if len(entries)*lenMultiple < cap(entries) {
		newEntries := make([]LogEntry, len(entries))
		copy(newEntries, entries)
		return newEntries
	}
	return entries
}

// Helper methods for Raft

// getFirstLog returns the first log entry
func (rf *Raft) getFirstLog() LogEntry {
	return rf.log[0]
}

// getLastLog returns the last log entry
func (rf *Raft) getLastLog() LogEntry {
	return rf.log[len(rf.log)-1]
}

// encodeState encodes the persistent state
func (rf *Raft) encodeState() []byte {
	w := new(bytes.Buffer)
	e := gob.NewEncoder(w)
	e.Encode(rf.currentTerm)
	e.Encode(rf.votedFor)
	e.Encode(rf.log)
	e.Encode(rf.lastIncludedIndex)
	e.Encode(rf.lastIncludedTerm)
	return w.Bytes()
}

// isLogUpToDate checks if the candidate's log is at least as up-to-date as receiver's log
func (rf *Raft) isLogUpToDate(candidateTerm, candidateIndex int) bool {
	lastLog := rf.getLastLog()
	return candidateTerm > lastLog.Term || (candidateTerm == lastLog.Term && candidateIndex >= lastLog.Index)
}

// matchLog checks if the log contains an entry at prevLogIndex whose term matches prevLogTerm
func (rf *Raft) matchLog(prevLogTerm, prevLogIndex int) bool {
	return prevLogIndex <= rf.getLastLog().Index && rf.log[prevLogIndex-rf.getFirstLog().Index].Term == prevLogTerm
}

// ChangeState changes the state of the Raft server
func (rf *Raft) ChangeState(state ServerState) {
	if rf.state == state {
		return
	}
	DPrintf("{Node %v} changes state from %v to %v in term %v", rf.me, rf.state, state, rf.currentTerm)
	rf.state = state
	switch state {
	case Follower:
		rf.heartbeatTimeout = StableHeartbeatTimeout()
		rf.electionTimeout = RandomElectionTimeout()
	case Candidate:
	case Leader:
		lastLog := rf.getLastLog()
		for i := range rf.nextIndex {
			rf.nextIndex[i] = lastLog.Index + 1
		}
		for i := range rf.matchIndex {
			rf.matchIndex[i] = 0
		}
	}
}

// advanceCommitIndexForFollower advances the commit index for followers
func (rf *Raft) advanceCommitIndexForFollower(leaderCommit int) {
	newCommitIndex := min(leaderCommit, rf.getLastLog().Index)
	if newCommitIndex > rf.commitIndex {
		DPrintf("{Node %v} advance commitIndex from %v to %v with leaderCommit %v in term %v", rf.me, rf.commitIndex, newCommitIndex, leaderCommit, rf.currentTerm)
		rf.commitIndex = newCommitIndex
		rf.applyCond.Signal()
	}
}

// advanceCommitIndexForLeader advances the commit index for leaders
func (rf *Raft) advanceCommitIndexForLeader() {
	n := len(rf.matchIndex)
	srt := make([]int, n)
	copy(srt, rf.matchIndex)
	insertionSort(srt)
	newCommitIndex := srt[n/2]
	if newCommitIndex > rf.commitIndex {
		// only commit log entries from current term
		if rf.matchLog(rf.currentTerm, newCommitIndex) {
			DPrintf("{Node %v} advance commitIndex from %v to %v with matchIndex %v in term %v", rf.me, rf.commitIndex, newCommitIndex, rf.matchIndex, rf.currentTerm)
			rf.commitIndex = newCommitIndex
			rf.applyCond.Signal()
		} else {
			DPrintf("{Node %v} can not advance commitIndex from %v to %v with matchIndex %v in term %v", rf.me, rf.commitIndex, newCommitIndex, rf.matchIndex, rf.currentTerm)
		}
	}
}


