package raft

import (
	"testing"
	"time"
)

func TestDebugFollowerFailure(t *testing.T) {
	servers := 3
	cfg := make_config(t, servers, false, false)
	defer cfg.cleanup()

	t.Logf("=== Starting debug test ===")

	// 提交第一个命令到所有服务器
	t.Logf("=== Step 1: Submit command 101 to all servers ===")
	index1 := cfg.one(101, servers, false)
	t.Logf("Command 101 committed at index %d", index1)

	// 检查所有服务器的状态
	for i := 0; i < servers; i++ {
		nd, cmd := cfg.nCommitted(index1)
		t.Logf("nCommitted(%d) = %d, cmd = %v", index1, nd, cmd)
	}

	// 找到领导者并断开一个跟随者
	t.Logf("=== Step 2: Disconnect one follower ===")
	leader1 := cfg.checkOneLeader()
	follower := (leader1 + 1) % servers
	t.Logf("Leader: %d, Disconnecting follower: %d", leader1, follower)
	cfg.disconnect(follower)

	// 等待一段时间让系统稳定
	time.Sleep(100 * time.Millisecond)

	// 尝试提交第二个命令
	t.Logf("=== Step 3: Submit command 102 to remaining servers ===")
	
	// 检查当前的领导者
	currentLeader := cfg.checkOneLeader()
	t.Logf("Current leader: %d", currentLeader)

	// 尝试提交命令102
	index2, _, ok := cfg.rafts[currentLeader].Start(102)
	if !ok {
		t.Fatalf("Leader %d rejected Start(102)", currentLeader)
	}
	t.Logf("Command 102 submitted at index %d", index2)

	// 等待提交
	t.Logf("=== Step 4: Wait for commitment ===")
	for i := 0; i < 100; i++ { // 等待最多2秒
		nd, cmd := cfg.nCommitted(index2)
		t.Logf("Iteration %d: nCommitted(%d) = %d, cmd = %v", i, index2, nd, cmd)
		
		if nd >= servers-1 {
			t.Logf("SUCCESS: Command 102 committed to %d servers", nd)
			return
		}
		
		time.Sleep(20 * time.Millisecond)
	}

	t.Fatalf("Command 102 failed to commit")
}
